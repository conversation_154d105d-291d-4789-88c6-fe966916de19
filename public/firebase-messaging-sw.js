importScripts(
  "https://www.gstatic.com/firebasejs/11.10.0/firebase-app-compat.js"
);
importScripts(
  "https://www.gstatic.com/firebasejs/11.10.0/firebase-messaging-compat.js"
);

// Use same config as main app
firebase.initializeApp({
  apiKey: "AIzaSyBCcXN22OaRGbzFRQq_-OubzJNemaIk99Q",
  authDomain: "slo-studio-424716.firebaseapp.com",
  projectId: "slo-studio-424716",
  storageBucket: "slo-studio-424716.firebasestorage.app",
  messagingSenderId: "268628417819",
  appId: "1:268628417819:web:f439004a64d0f68f1dc7be",
});

const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage(function (payload) {
  console.log("🔔 Background message received:", payload);

  const notificationTitle = payload.notification?.title || "New Notification";
  const notificationOptions = {
    body: payload.notification?.body || "",
    icon: "/favicon.ico",
    badge: "/favicon.ico",
    data: payload.data,
    requireInteraction: false,
    silent: false,
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});
