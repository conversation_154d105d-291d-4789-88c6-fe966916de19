/**
 * Expo Theme Configuration
 * 
 * Converted from React Native Stream Chat theme to web CSS variables
 * Ensures consistency between mobile and web platforms
 */

export const expoTheme = {
  colors: {
    // Core Background Colors
    primary: 'black',                    // Main background
    secondary: '#1A1A1A',               // Message bubbles, containers
    tertiary: '#4a4a4a',                // Reply containers, secondary elements
    dark: '#131313',                    // Targeted messages, deeper backgrounds
    
    // Text Colors
    text: '#FFFFFF',                    // Primary text (white)
    textSecondary: '#72767E',           // Meta text, timestamps
    textMuted: '#4a4a4a',               // Disabled, placeholder text
    
    // Accent Colors
    accent: '#005FFF',                  // Links, autolinks
    error: '#EF5252',                   // Error states, notifications
    success: '#4CAF50',                 // Success states
    warning: '#FF9800',                 // Warning states
    
    // Border Colors
    border: 'transparent',              // Most borders are transparent
    borderSecondary: '#4a4a4a',         // Reply borders, curves
    borderTertiary: 'black',            // Reaction containers
    
    // Avatar & Image
    avatarBackground: 'white',          // Group avatar background
    
    // Special States
    unread: 'transparent',              // Unread indicators background
    transparent: 'transparent',         // Explicit transparent
  },
  
  spacing: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 20,
    xxl: 24,
  },
  
  borderRadius: {
    small: 8,
    medium: 12,                         // Message bubbles
    large: 20,                          // Replies container
    circle: 50,                         // Circular elements
  },
  
  typography: {
    fontSize: {
      xs: 12,
      sm: 14,
      md: 16,                           // Channel preview title
      lg: 18,
      xl: 20,
    },
    fontWeight: {
      regular: '400',
      medium: '500',
      semibold: '600',
      bold: '700',                      // Channel preview title
    },
    lineHeight: {
      tight: 12,                        // Channel preview message
      normal: 1.4,
      relaxed: 1.6,
    },
  },
  
  avatar: {
    small: 14,                          // Replies avatar
    medium: 50,                         // Channel preview avatar
    large: 64,
  },
  
  animation: {
    duration: 1800,                     // Skeleton animation time
  },
} as const;

export type ExpoTheme = typeof expoTheme;

/**
 * Generate CSS variables from Expo theme
 */
export function generateExpoCSSVariables(theme: ExpoTheme = expoTheme): Record<string, string> {
  const cssVariables: Record<string, string> = {};
  
  // Colors
  Object.entries(theme.colors).forEach(([key, value]) => {
    const cssKey = `--expo-${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
    cssVariables[cssKey] = value;
  });
  
  // Spacing
  Object.entries(theme.spacing).forEach(([key, value]) => {
    const cssKey = `--expo-spacing-${key}`;
    cssVariables[cssKey] = `${value}px`;
  });
  
  // Border radius
  Object.entries(theme.borderRadius).forEach(([key, value]) => {
    const cssKey = `--expo-border-radius-${key}`;
    cssVariables[cssKey] = typeof value === 'number' ? `${value}px` : value;
  });
  
  // Typography
  Object.entries(theme.typography.fontSize).forEach(([key, value]) => {
    const cssKey = `--expo-font-size-${key}`;
    cssVariables[cssKey] = `${value}px`;
  });
  
  Object.entries(theme.typography.fontWeight).forEach(([key, value]) => {
    const cssKey = `--expo-font-weight-${key}`;
    cssVariables[cssKey] = value;
  });
  
  Object.entries(theme.typography.lineHeight).forEach(([key, value]) => {
    const cssKey = `--expo-line-height-${key}`;
    cssVariables[cssKey] = typeof value === 'number' ? `${value}px` : value.toString();
  });
  
  // Avatar sizes
  Object.entries(theme.avatar).forEach(([key, value]) => {
    const cssKey = `--expo-avatar-${key}`;
    cssVariables[cssKey] = `${value}px`;
  });
  
  return cssVariables;
}

/**
 * Map Expo theme to Stream Chat CSS variables
 */
export function generateStreamCSSVariables(theme: ExpoTheme = expoTheme): Record<string, string> {
  return {
    // Background colors
    '--str-chat__primary-color': theme.colors.accent,
    '--str-chat__background-color': theme.colors.primary,
    '--str-chat__secondary-background-color': theme.colors.secondary,
    '--str-chat__surface-color': theme.colors.secondary,
    '--str-chat__border-color': theme.colors.border,
    
    // Text colors
    '--str-chat__primary-text-color': theme.colors.text,
    '--str-chat__secondary-text-color': theme.colors.textSecondary,
    '--str-chat__disabled-text-color': theme.colors.textMuted,
    
    // Message specific
    '--str-chat__message-bubble-color': theme.colors.secondary,
    '--str-chat__message-text-color': theme.colors.text,
    '--str-chat__message-border-radius': `${theme.borderRadius.medium}px`,
    
    // Channel list
    '--str-chat__channel-preview-background': theme.colors.primary,
    '--str-chat__channel-preview-title-color': theme.colors.text,
    '--str-chat__channel-preview-message-color': theme.colors.textSecondary,
    
    // Input
    '--str-chat__input-background': theme.colors.primary,
    '--str-chat__input-text-color': theme.colors.text,
    '--str-chat__input-border-color': theme.colors.border,
    
    // Reactions
    '--str-chat__reaction-background': theme.colors.secondary,
    '--str-chat__reaction-border-color': theme.colors.borderSecondary,
    
    // Error states
    '--str-chat__error-color': theme.colors.error,
    '--str-chat__success-color': theme.colors.success,
  };
}