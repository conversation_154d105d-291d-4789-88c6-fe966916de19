html,
body,
#root {
  height: 100%;
}
body {
  margin: 0;
}
#root {
  display: flex;
}

.str-chat__channel-list {
  flex: 1;
  background-color: var(--expo-primary, black) !important;
  border: none;
  overflow: hidden;
}

.str-chat {
  border: none !important;
}

/* Ensure all nested elements in channel list have primary background */
.str-chat__channel-list,
.str-chat__channel-list-messenger,
.str-chat__channel-list-messenger__main {
  background-color: var(--expo-primary, black) !important;
}
.str-chat__channel-list-messenger__main {
  padding: 8px;
  border: none;
}
.str-chat__channel {
  width: 100%;
}
.str-chat__thread {
  width: 45%;
}

/* Hide default Stream Chat header */
.str-chat__channel-list-header {
  display: none !important;
}

/* Custom Channel List Container */
.channel-list-container {
  width: 30%;
  max-width: 300px;
  background-color: var(--expo-primary, black);
  border-right: 1px solid var(--expo-border-secondary, #4a4a4a);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.custom-channel-list-header {
  background-color: var(--expo-primary, black);
  padding: var(--expo-spacing-xl, 20px) var(--expo-spacing-md, 12px) var(--expo-spacing-md, 12px) var(--expo-spacing-md, 12px);
  flex-shrink: 0;
}

/* Channel Preview Customization */
.str-chat__channel-preview {
  padding: var(--expo-spacing-sm, 8px) var(--expo-spacing-md, 12px);
  border-bottom: 1px solid var(--expo-border-secondary, #4a4a4a);
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.str-chat__channel-preview:hover {
  background-color: var(--expo-tertiary, #4a4a4a);
}

.str-chat__channel-preview--active {
  background-color: var(--expo-accent, #005FFF) !important;
}

.str-chat__channel-preview--active:hover {
  background-color: var(--expo-accent, #005FFF) !important;
  opacity: 0.8;
}

/* Channel Preview Content */
.str-chat__channel-preview__content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.str-chat__channel-preview__content-wrapper {
  flex: 1;
  min-width: 0;
}

.str-chat__channel-preview__content-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.str-chat__channel-preview__content-name {
  color: var(--expo-text, #FFFFFF);
  font-weight: var(--expo-font-weight-bold, 700);
  font-size: var(--expo-font-size-md, 16px);
  truncate: true;
}

.str-chat__channel-preview__content-time {
  color: var(--expo-text-secondary, #72767E);
  font-size: var(--expo-font-size-xs, 12px);
}

.str-chat__channel-preview__content-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.str-chat__channel-preview__content-info {
  color: var(--expo-text-secondary, #72767E);
  font-size: var(--expo-font-size-sm, 14px);
  line-height: var(--expo-line-height-tight, 12px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

/* Avatar Customization */
.str-chat__avatar {
  border-radius: 50%;
  flex-shrink: 0;
}

/* Unread Count Badge */
.str-chat__channel-preview__unread-count {
  background-color: var(--expo-error, #EF5252);
  color: var(--expo-text, white);
  border-radius: var(--expo-border-radius-circle, 50%);
  min-width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--expo-font-size-xs, 12px);
  font-weight: var(--expo-font-weight-semibold, 600);
}

/* Loading State */
.str-chat__channel-list__loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--expo-spacing-xxl, 24px);
  color: var(--expo-text-secondary, #72767E);
}

/* Empty State */
.str-chat__channel-list__empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--expo-spacing-xxl, 24px);
  color: var(--expo-text-secondary, #72767E);
  text-align: center;
}

/* Scrollbar Customization */
.str-chat__channel-list .str-chat__channel-list-messenger__main {
  background-color: var(--expo-primary, black) !important;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.str-chat__channel-list
  .str-chat__channel-list-messenger__main::-webkit-scrollbar {
  display: none; /* WebKit */
}

/* Custom Channel Preview Styles - Now using Tailwind in React component */

/* Message Area Styling */
.str-chat__message-list {
  background-color: var(--expo-primary, black) !important;
  padding: var(--expo-spacing-md, 12px);
}

.str-chat__message-list-scroll {
  background-color: var(--expo-primary, black) !important;
}

.str-chat__message-simple {
  background-color: transparent !important;
}

.str-chat__message-simple__content {
  background-color: transparent !important;
}

/* Style default message input to match mobile design */

.str-chat__message-bubble {
  background-color: var(--expo-secondary, #1A1A1A) !important;
}

.str-chat__message-input {
  background-color: var(--expo-primary, black) !important;
  border-top: 1px solid var(--expo-border-secondary, #4a4a4a) !important;
  padding: var(--expo-spacing-md, 12px) !important;
}

.str-chat__message-input-wrapper {
  background-color: var(--expo-secondary, #1A1A1A) !important;
  border-radius: var(--expo-border-radius-large, 20px) !important;
  border: none !important;
}

.str-chat__message-textarea__textarea {
  background-color: var(--expo-tertiary, #4a4a4a) !important;
  color: var(--expo-text, white) !important;
  border: none !important;
  border-radius: var(--expo-border-radius-large, 20px) !important;
  padding: var(--expo-spacing-sm, 8px) var(--expo-spacing-md, 12px) !important;
}

.str-chat__message-textarea__textarea::placeholder {
  color: var(--expo-text-muted, #4a4a4a) !important;
}

/* Hide default channel header */
.str-chat__channel-header {
  display: none !important;
}

/* ===== DEFAULT MESSAGE STYLING (Mobile Design) ===== */

/* Message container - mobile style layout */
.str-chat__message-simple {
}

/* Outgoing messages (right side) */
.str-chat__message-simple--me {
}

/* Incoming messages (left side) */
.str-chat__message-simple:not(.str-chat__message-simple--me) {
}

/* Message content wrapper */
.str-chat__message-simple__content {
  border: 1px solid red !important;
  background: yellow !important;
}

/* Message bubble styling */
.str-chat__message-text {
  background-color: var(--expo-secondary, #1A1A1A) !important;
  color: var(--expo-text, white);
  padding: var(--expo-spacing-sm, 8px) var(--expo-spacing-md, 12px);
  border-radius: var(--expo-border-radius-medium, 12px);
  margin: 0;
  max-width: fit-content;
  font-size: var(--expo-font-size-sm, 14px);
  line-height: 1.4;
}

/* Outgoing message bubbles - darker gray */
.str-chat__message-simple--me .str-chat__message-text {
  background-color: var(--expo-secondary, #1A1A1A) !important;
}

/* Message text content */
.str-chat__message-text-inner {
}

/* Avatar image */
.str-chat__avatar-image {
  width: var(--expo-avatar-medium, 50px) !important;
  height: var(--expo-avatar-medium, 50px) !important;
  object-fit: cover !important;
}

/* Avatar fallback */
.str-chat__avatar-fallback {
}

/* Message status indicators (read receipts, etc.) */
.str-chat__message-simple-status {
  color: var(--expo-text-secondary, #72767E) !important;
  font-size: var(--expo-font-size-xs, 12px) !important;
}

/* Date separators */
.str-chat__date-separator {
  font-size: var(--expo-font-size-xs, 12px) !important;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--expo-text-secondary, #72767E) !important;
  padding: var(--expo-spacing-md, 12px) 0 !important;
}

.str-chat__date-separator-line {
  display: none !important;
}

/* Thread replies */
.str-chat__message-replies-count-button {
  background-color: transparent !important;
  color: var(--expo-accent, #005FFF) !important;
  border: none !important;
  padding: var(--expo-spacing-sm, 8px) 0 !important;
  font-size: var(--expo-font-size-xs, 12px) !important;
  margin-top: var(--expo-spacing-sm, 8px) !important;
  display: flex !important;
  align-items: center !important;
  gap: var(--expo-spacing-sm, 8px) !important;
}

.str-chat__message-replies-count-button:hover {
  color: var(--expo-accent, #005FFF) !important;
  opacity: 0.8;
}

.str-chat__message-simple-status-number {
  display: none !important;
}

.str-chat__avatar--message-status {
  display: none !important;
}

.str-chat__li--single {
  /* margin-bottom: 20px !important; */
}

.str-chat__li--middle .str-chat__message > .str-chat__message-metadata {
  /* display: none !important; */
}

.str-chat__message-metadata {
  display: flex;
  align-items: flex-end !important;
  justify-content: flex-end !important;
  gap: 0.25rem !important;
  padding-top: 2px !important;
}

.str-chat__message-simple-name {
  font-size: var(--expo-font-size-xs, 12px) !important;
  color: var(--expo-text, white) !important;
  font-weight: var(--expo-font-weight-bold, 700) !important;
}

.str-chat__message-simple-timestamp {
  font-size: var(--expo-font-size-xs, 12px) !important;
  color: var(--expo-text-secondary, #72767E) !important;
}

.str-chat__message-reaction:hover {
  background-color: var(--expo-tertiary, #4a4a4a) !important;
  border-color: var(--expo-border-secondary, #4a4a4a) !important;
}

.str-chat__message-reaction--own {
  background-color: var(--expo-accent, #005FFF) !important;
  border-color: var(--expo-accent, #005FFF) !important;
  color: var(--expo-text, white) !important;
}

.str-chat__message-reaction--own:hover {
  background-color: var(--expo-accent, #005FFF) !important;
  border-color: var(--expo-accent, #005FFF) !important;
  opacity: 0.8;
}

.str-chat__message-reaction-emoji {
  font-size: 12px !important;
  line-height: 1 !important;
  margin: 0 !important;
}

.str-chat__message-reaction-count {
  font-size: 10px !important;
  font-weight: var(--expo-font-weight-semibold, 600) !important;
  line-height: 1 !important;
  margin: 0 !important;
  color: var(--expo-text-secondary, #72767E) !important;
}

.str-chat__message-reaction--own .str-chat__message-reaction-count {
  color: var(--expo-text, white) !important;
}

/* Position reactions at the end of message content */
.str-chat__message-simple-content {
  position: relative !important;
}

/* ===== THREAD STYLING ===== */

/* Thread container */
.str-chat__thread {
  background-color: var(--expo-primary, black) !important;
  border-left: 1px solid var(--expo-border-secondary, #4a4a4a) !important;
  width: 45% !important;
  display: flex !important;
  flex-direction: column !important;
  height: 100vh !important;
  position: relative !important;
}

/* Thread wrapper */
.str-chat__thread-wrapper {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

/* Thread header */
.str-chat__thread-header {
  background-color: var(--expo-primary, black) !important;
  border-bottom: 1px solid var(--expo-border-secondary, #4a4a4a) !important;
  padding: var(--expo-spacing-md, 12px) !important;
  color: var(--expo-text, white) !important;
  flex-shrink: 0 !important;
}

.str-chat__thread-header-details {
  color: var(--expo-text, white) !important;
}

.str-chat__thread-header-title {
  color: var(--expo-text, white) !important;
  font-size: var(--expo-font-size-lg, 18px) !important;
  font-weight: var(--expo-font-weight-semibold, 600) !important;
}

.str-chat__thread-header-subtitle {
  color: var(--expo-text-secondary, #72767E) !important;
  font-size: var(--expo-font-size-sm, 14px) !important;
}

/* Thread close button */
.str-chat__square-button {
  background-color: transparent !important;
  color: var(--expo-text, white) !important;
  border: none !important;
  padding: var(--expo-spacing-sm, 8px) !important;
  border-radius: var(--expo-border-radius-small, 8px) !important;
}

.str-chat__square-button:hover {
  background-color: var(--expo-tertiary, #4a4a4a) !important;
}

/* Thread message list */
.str-chat__thread .str-chat__message-list {
  background-color: var(--expo-primary, black) !important;
  padding: var(--expo-spacing-md, 12px) !important;
  flex: 1 !important;
  overflow-y: auto !important;
  display: flex !important;
  flex-direction: column !important;
}

.str-chat__thread .str-chat__message-list-scroll {
  background-color: var(--expo-primary, black) !important;
  flex: 1 !important;
  overflow-y: auto !important;
}

/* Thread messages */
.str-chat__thread .str-chat__message-simple {
  background-color: transparent !important;
  margin-bottom: 0.5rem !important;
}

.str-chat__thread .str-chat__message-text {
  background-color: var(--expo-secondary, #1A1A1A) !important;
  color: var(--expo-text, white) !important;
  padding: var(--expo-spacing-sm, 8px) var(--expo-spacing-md, 12px) !important;
  border-radius: var(--expo-border-radius-medium, 12px) !important;
  font-size: var(--expo-font-size-sm, 14px) !important;
  line-height: 1.4 !important;
}

/* Thread message input */
.str-chat__thread .str-chat__message-input {
  background-color: var(--expo-primary, black) !important;
  border-top: 1px solid var(--expo-border-secondary, #4a4a4a) !important;
  padding: var(--expo-spacing-md, 12px) !important;
  flex-shrink: 0 !important;
  margin-top: auto !important;
}

.str-chat__thread .str-chat__message-input-wrapper {
  background-color: var(--expo-secondary, #1A1A1A) !important;
  border-radius: var(--expo-border-radius-large, 20px) !important;
  border: none !important;
}

.str-chat__thread .str-chat__message-textarea__textarea {
  background-color: var(--expo-tertiary, #4a4a4a) !important;
  color: var(--expo-text, white) !important;
  border: none !important;
  border-radius: var(--expo-border-radius-large, 20px) !important;
  padding: var(--expo-spacing-sm, 8px) var(--expo-spacing-md, 12px) !important;
}

.str-chat__thread .str-chat__message-textarea__textarea::placeholder {
  color: var(--expo-text-muted, #4a4a4a) !important;
}

/* Thread message metadata */
.str-chat__thread .str-chat__message-metadata {
  display: flex !important;
  align-items: flex-end !important;
  justify-content: flex-end !important;
  gap: 0.25rem !important;
  padding-top: 2px !important;
}

.str-chat__thread .str-chat__message-simple-name {
  font-size: var(--expo-font-size-xs, 12px) !important;
  color: var(--expo-text, white) !important;
  font-weight: var(--expo-font-weight-bold, 700) !important;
}

.str-chat__thread .str-chat__message-simple-timestamp {
  font-size: 8px !important;
  color: #9ca3af !important;
}

/* Thread send button */
.str-chat__thread .str-chat__send-button {
  background-color: transparent !important;
  border: none !important;
  border-radius: 50% !important;
  color: white !important;
  padding: 0.5rem !important;
  margin-left: 0.5rem !important;
}

.str-chat__thread .str-chat__send-button:hover {
  background-color: #374151 !important;
}

/* Thread avatar styling */
.str-chat__thread .str-chat__avatar-image {
  width: var(--expo-avatar-small, 32px) !important;
  height: var(--expo-avatar-small, 32px) !important;
  object-fit: cover !important;
}


.str-chat__thread .str-chat__message-reaction {
  border: 1px solid var(--expo-border-secondary, #4a4a4a) !important;
  border-radius: var(--expo-border-radius-medium, 12px) !important;
  padding: var(--expo-spacing-xs, 4px) !important;
  font-size: var(--expo-font-size-xs, 12px) !important;
  min-height: auto !important;
  height: 20px !important;
  display: flex !important;
  align-items: center !important;
  gap: var(--expo-spacing-xs, 4px) !important;
  margin: 0 !important;
}

/* Thread empty state */
.str-chat__thread-start {
  background-color: var(--expo-primary, black) !important;
  color: var(--expo-text-secondary, #72767E) !important;
  text-align: center !important;
  padding: var(--expo-spacing-xxl, 24px) !important;
}

/* Thread loading state */
.str-chat__thread .str-chat__loading-indicator {
  color: var(--expo-text-secondary, #72767E) !important;
}
