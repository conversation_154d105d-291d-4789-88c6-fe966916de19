import { useChannelStateContext } from "stream-chat-react";

export const CustomChannelHeader = () => {
  try {
    const { channel } = useChannelStateContext();

    // Early return if no channel is selected
    if (!channel) {
      return (
        <div className="bg-black border-b border-gray-800 px-4 py-3">
          <div className="flex items-center gap-3">
            <button className="w-8 h-8 flex items-center justify-center text-white hover:text-gray-300 transition-colors">
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
            <h2 className="text-white text-lg font-semibold">Select a chat</h2>
          </div>
        </div>
      );
    }

    // Get channel name or member names (similar to our channel preview logic)
    const members = Object.values(channel?.state?.members || {});
    const currentUserId = channel?._client?.user?.id;
    const otherMember = members.find(
      (member: any) => member.user_id !== currentUserId
    ) as any;

    const channelName =
      (channel?.data as any)?.name || otherMember?.user?.name || "Chat";
    const memberImage = otherMember?.user?.image;

    return (
      <div className="bg-black border-b border-gray-800 px-4 py-3">
        <div className="flex items-center gap-3">
          {/* Back Arrow */}
          <button className="w-8 h-8 flex items-center justify-center text-white hover:text-gray-300 transition-colors">
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>

          {/* Avatar */}
          <div className="w-10 h-10 rounded-full overflow-hidden flex-shrink-0">
            {memberImage ? (
              <img
                src={memberImage}
                alt={channelName}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-indigo-400 to-purple-600 flex items-center justify-center text-white font-semibold text-sm">
                {channelName.charAt(0).toUpperCase()}
              </div>
            )}
          </div>

          {/* Channel Name */}
          <h2 className="text-white text-lg font-semibold">{channelName}</h2>
        </div>
      </div>
    );
  } catch (error) {
    // Fallback if useChannelStateContext is not available
    return (
      <div className="bg-black border-b border-gray-800 px-4 py-3">
        <div className="flex items-center gap-3">
          <button className="w-8 h-8 flex items-center justify-center text-white hover:text-gray-300 transition-colors">
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
          <h2 className="text-white text-lg font-semibold">Chat</h2>
        </div>
      </div>
    );
  }
};
