import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Loader2 } from "lucide-react";
import { useChannelStateContext } from "stream-chat-react";
import type { StreamMessage } from "stream-chat";

interface MessageStatusProps {
  message: StreamMessage;
}

export const MessageStatus: React.FC<MessageStatusProps> = ({ message }) => {
  const { channel } = useChannelStateContext();

  // Check multiple possible ways to determine read status
  const members = channel?.state?.members || {};
  const reads = channel?.state?.read || {};

  // Check if message has been read by checking read state
  let isRead = false;
  if (channel && message.created_at) {
    const messageTime = new Date(message.created_at).getTime();
    // Check if any other member has read past this message
    Object.values(reads).forEach((readState) => {
      if (readState.user?.id !== message.user?.id && readState.last_read) {
        const lastReadTime = new Date(readState.last_read).getTime();
        if (lastReadTime >= messageTime) {
          isRead = true;
        }
      }
    });
  }

  // Fallback to original readBy check
  if (!isRead && message.readBy && message.readBy.length > 0) {
    isRead = true;
  }

  const isSending = message.status === "sending";
  const isFailed = message.status === "failed";

  if (isFailed) {
    return <X className="w-3 h-3 text-red-500" />;
  }

  if (isSending) {
    return <Loader2 className="w-3 h-3 text-gray-400 animate-spin" />;
  }

  if (isRead) {
    // Double checkmark for read
    return (
      <div className="flex">
        <CheckCheck className="w-3 h-3 text-blue-500" />
      </div>
    );
  }

  // Single checkmark for delivered
  return <Check className="w-3 h-3 text-gray-500" />;
};