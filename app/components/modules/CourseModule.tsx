import { useState } from "react";
import { Link } from "react-router";
import {
  MoreHorizontal,
  MonitorPlay,
  Volume2,
  File,
  List,
  Plus,
  CircleCheck,
  CheckCircle,
} from "lucide-react";
import type {
  CourseModule as CourseModuleType,
  Course,
  Section as APISection,
  Lesson,
} from "~/lib/api/types";
import { useMyGroups } from "~/lib/api/client-queries";

interface SectionWithExpanded extends APISection {
  expanded: boolean;
}

interface CourseModuleProps {
  module: CourseModuleType;
  courseData?: Course;
  params: {
    groupId: string;
    cohortId: string;
    moduleId: string;
  };
}

// Helper function to get lesson content type
function getLessonContentType(
  lesson: Lesson
): "video" | "audio" | "article" | "mixed" {
  if (!lesson.lessonContents || lesson.lessonContents.length === 0) {
    return "article";
  }

  console.log(lesson.lessonContents);

  const contentTypes = lesson.lessonContents.map(
    (content) => content.contentType
  );

  if (contentTypes.includes("video")) return "video";
  if (contentTypes.includes("audio")) return "audio";
  if (contentTypes.includes("article")) return "article";

  return "article";
}

// Helper function to get lesson icon based on content type
function getLessonIcon(contentType: "video" | "audio" | "article" | "mixed") {
  switch (contentType) {
    case "video":
      return <MonitorPlay className="w-5 h-5" />;
    case "audio":
      return <Volume2 className="w-5 h-5" />;
    case "article":
    default:
      return <File className="w-5 h-5" />;
  }
}

// Helper function to format duration
function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${String(minutes).padStart(2, "0")}:${String(
    remainingSeconds
  ).padStart(2, "0")}`;
}

export default function CourseModule({
  module,
  courseData,
  params,
}: CourseModuleProps) {
  const { groupId, cohortId } = params;

  // Fetch group data to get banner image
  const { data: myGroupsResponse } = useMyGroups();
  const group = myGroupsResponse?.groups.byId[params.groupId];

  // Transform course data into sections format
  const initialSections: SectionWithExpanded[] = courseData?.sections
    ? courseData.sections
        .sort((a, b) => a.sectionOrder - b.sectionOrder)
        .map((section, index) => ({
          ...section,
          expanded: index === 0, // Expand first section by default
        }))
    : [];

  const [sections, setSections] =
    useState<SectionWithExpanded[]>(initialSections);

  const courseProgress = courseData?.completionPercentage || 0;

  return (
    <div className="bg-black min-h-screen">
      {/* Top Bar */}
      <div className="sticky top-0 z-50 bg-black/10 backdrop-blur-md border-b border-zinc-800">
        <div className="mx-auto px-8 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="font-semibold text-white">
                {courseData?.name || module.name}
              </h1>
              <p className="text-sm text-zinc-400">
                {courseData?.totalSections || sections.length} Sections •{" "}
                {courseData?.totalLessons || 0} Lessons
              </p>
            </div>
            <button className="p-2 hover:bg-zinc-800 rounded-lg transition-colors">
              <MoreHorizontal className="w-5 h-5 text-zinc-400" />
            </button>
          </div>
        </div>
      </div>

      {/* Header with banner */}
      <div className="relative h-72 bg-zinc-900">
        <div
          className="w-full h-full bg-cover bg-top"
          style={{
            backgroundImage: courseData?.bannerImage
              ? `url(${courseData.bannerImage})`
              : group?.bannerImage
                ? `url(${group.bannerImage})`
                : `linear-gradient(135deg, #1e293b 0%, #0f172a 100%)`,
          }}
        >
          <div className="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-transparent" />
        </div>

        {/* Progress indicator */}
      </div>

      {/* Course Content */}
      <div className="max-w-4xl mx-auto px-8 translate-y-[-50px]">
        <div className=" bg-zinc-500/20 mb-6 rounded-lg  p-4 backdrop-blur-sm">
          <div className="flex items-end justify-between mb-3">
            <div>
              <p className="text-white text-sm mb-1">Continue Learning</p>
              <p className="text-zinc-400 text-xs">
                Chapter 3: The Micro Structure, Ecns & Dark Pool
              </p>
            </div>
            <span className="text-white text-xl font-bold">
              {courseProgress}%
            </span>
          </div>
          <div className="flex items-center justify-end space-x-2">
            <div className="w-full h-1 bg-zinc-700 rounded-full overflow-hidden">
              <div
                className="h-full bg-red-500 rounded-full transition-all duration-300"
                style={{ width: `${courseProgress}%` }}
              ></div>
            </div>
          </div>
        </div>
        <div className="mb-8">
          <h2 className="text-white text-lg font-semibold mb-2">
            Course Content
          </h2>
          <div className="flex items-center space-x-6 text-zinc-400 text-sm">
            <div className="flex items-center space-x-2">
              <List className="w-4 h-4" />
              <span>
                {courseData?.totalSections || sections.length} Sections
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <File className="w-4 h-4" />
              <span>{courseData?.totalLessons || 0} Lessons</span>
            </div>
          </div>
        </div>

        <div className="space-y-6">
          {sections.map((section) => (
            <div
              key={section.id}
              className="rounded-lg overflow-hidden border border-zinc-800"
            >
              {/* Section Header */}

              <h3 className="text-white font-semibold text-left px-6 py-4">
                {section.name}
              </h3>

              {/* Section Lessons */}
              <div className="divide-y divide-zinc-800">
                {section.lessons
                  .sort((a, b) => a.lessonOrder - b.lessonOrder)
                  .map((lesson) => (
                    <Link
                      key={lesson.id}
                      to={`/groups/${groupId}/cohorts/${cohortId}/courses/${courseData?.externalId}/lessons/${lesson.id}`}
                      state={{ moduleId: params.moduleId }}
                      className="flex items-center justify-between px-6 py-4 hover:bg-zinc-800/30 transition-colors"
                    >
                      <div className="flex items-center gap-3 grow">
                        <div className="text-zinc-400">
                          {getLessonIcon(getLessonContentType(lesson))}
                        </div>
                        <h4 className="text-white truncate text-sm">
                          {lesson.title}
                        </h4>
                      </div>
                      <span className="text-zinc-400 text-sm">
                        {lesson.isCompleted ? (
                          <CircleCheck className="w-6 h-6 text-green-500" />
                        ) : (
                          <div className="text-zinc-400">
                            {formatDuration(90)}
                          </div>
                        )}
                      </span>
                    </Link>
                  ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
