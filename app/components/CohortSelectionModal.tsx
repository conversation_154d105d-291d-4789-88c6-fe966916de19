import { useState, useEffect } from "react";
import { X, Users } from "lucide-react";
import { useNavigate } from "react-router";
import type { GroupCohort } from "~/lib/api/types";
import { useJoinCohort } from "~/lib/api/client-queries";

interface CohortSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  cohorts: GroupCohort[];
  groupId: string | number;
  joinedCohortIds?: number[];
}

function formatDateRange(startDate: string, endDate: string) {
  const start = new Date(startDate);
  const end = new Date(endDate);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      day: "numeric",
      month: "short",
      year: "numeric",
    });
  };

  return `${formatDate(start)} - ${formatDate(end)}`;
}

export function CohortSelectionModal({
  isOpen,
  onClose,
  cohorts,
  groupId,
  joinedCohortIds = [],
}: CohortSelectionModalProps) {
  const navigate = useNavigate();
  const [selectedCohortId, setSelectedCohortId] = useState<string | null>(null);

  const joinCohortMutation = useJoinCohort();

  // Handle successful join
  useEffect(() => {
    if (joinCohortMutation.isSuccess && selectedCohortId) {
      navigate(`/groups/${groupId}/cohorts/${selectedCohortId}`);
      onClose();
    }
  }, [
    joinCohortMutation.isSuccess,
    selectedCohortId,
    groupId,
    navigate,
    onClose,
  ]);

  // Handle error
  useEffect(() => {
    if (joinCohortMutation.isError) {
      alert(
        joinCohortMutation.error?.message ||
          "Failed to join cohort. Please try again."
      );
    }
  }, [joinCohortMutation.isError, joinCohortMutation.error]);

  // Reset selection when modal opens
  useEffect(() => {
    if (isOpen) {
      setSelectedCohortId(null);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  // Filter out default cohorts and sort by date
  const availableCohorts = cohorts
    .filter((cohort) => !cohort.isDefault)
    .sort(
      (a, b) =>
        new Date(a.startDate).getTime() - new Date(b.startDate).getTime()
    );

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative bg-zinc-900 border border-zinc-800 rounded-2xl w-full max-w-2xl mx-4 max-h-[92vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-zinc-800">
          <h2 className="text-xl font-semibold text-white">Join New Cohort</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-zinc-800 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-zinc-400" />
          </button>
        </div>

        {/* Cohort List */}
        <div className="flex-1 overflow-y-auto p-6 space-y-5">
          {availableCohorts.map((cohort) => {
            const isJoined = joinedCohortIds.includes(cohort.id);
            const isSelected = selectedCohortId === cohort.externalId;

            return (
              <button
                key={cohort.id}
                onClick={() =>
                  !isJoined && setSelectedCohortId(cohort.externalId)
                }
                disabled={isJoined}
                className={`
                  w-full p-6 rounded-xl border transition-all text-left
                  ${
                    isJoined
                      ? "bg-zinc-800/60 border-zinc-700 opacity-50 cursor-not-allowed"
                      : isSelected
                        ? "bg-zinc-900 border-indigo-500"
                        : "bg-zinc-900 border-zinc-800 hover:border-zinc-600 hover:bg-zinc-900/80  cursor-pointer"
                  }
                `}
              >
                <div className="flex items-start justify-between gap-4">
                  {/* Left section: name, dates, avatars */}
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-semibold text-white mb-1 truncate">
                      {cohort.name}
                    </h3>
                    {cohort.startDate && cohort.endDate && (
                      <p className="text-sm text-zinc-400 mb-4">
                        {formatDateRange(cohort.startDate, cohort.endDate)}
                      </p>
                    )}

                    <div className="flex items-center gap-2">
                      {/* Placeholder avatar group */}
                      <div className="flex -space-x-2 overflow-hidden">
                        {Array.from({ length: 4 }).map((_, idx) => (
                          <img
                            key={idx}
                            src={`https://i.pravatar.cc/40?img=${idx + 1}`}
                            alt="member avatar"
                            className="w-6 h-6 rounded-full border-2 border-zinc-900 object-cover"
                          />
                        ))}
                      </div>
                      <span className="text-sm text-zinc-400 ml-2 whitespace-nowrap">
                        32 Members
                      </span>
                    </div>
                  </div>

                  {/* Right section: price pill */}
                  <div
                    className={`self-start px-4 py-1.5 rounded-full text-sm font-medium whitespace-nowrap
                    ${
                      isJoined
                        ? "bg-zinc-700 text-zinc-300"
                        : cohort.name.toLowerCase().includes("vip")
                          ? "bg-indigo-600 text-white"
                          : "bg-red-500 text-white"
                    }`}
                  >
                    {isJoined
                      ? "Joined"
                      : cohort.name.toLowerCase().includes("vip")
                        ? "$99 / year"
                        : "Free"}
                  </div>
                </div>
              </button>
            );
          })}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-zinc-800">
          <button
            onClick={() =>
              selectedCohortId &&
              joinCohortMutation.mutate({
                groupId: String(groupId),
                cohortId: selectedCohortId,
              })
            }
            disabled={!selectedCohortId || joinCohortMutation.isPending}
            className={`
              w-full py-3 px-6 rounded-lg font-semibold transition-all text-center
              ${
                selectedCohortId && !joinCohortMutation.isPending
                  ? "bg-indigo-600 text-white hover:bg-indigo-700"
                  : "bg-zinc-800 text-zinc-500 cursor-not-allowed"
              }
            `}
          >
            {joinCohortMutation.isPending ? "Joining..." : "Join Cohort"}
          </button>
        </div>
      </div>
    </div>
  );
}
