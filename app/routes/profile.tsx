import { useState } from "react";
import { useNavigate } from "react-router";
import { useProfile, useEditProfile } from "~/lib/api/client-queries";
import { Camera, Edit2, Save, X, LogOut } from "lucide-react";
import Session from "supertokens-web-js/recipe/session";
import { useAppContext } from "~/lib/providers/app-context";
import { queryClient } from "~/lib/providers/query-client";

export default function ProfilePage() {
  const navigate = useNavigate();
  const { cleanup } = useAppContext();
  const { data: profileData, isLoading } = useProfile();
  const editProfileMutation = useEditProfile();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    interestedTopics: [] as string[],
    phoneNumber: "",
    gender: null as string | null,
  });

  const profile = profileData?.data;

  const handleEdit = () => {
    if (profile) {
      setFormData({
        firstName: profile.firstName || "",
        lastName: profile.lastName || "",
        interestedTopics: profile.interestedTopics || [],
        phoneNumber: profile.phoneNumber || "",
        gender: profile.gender,
      });
      setIsEditing(true);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
  };

  const handleSave = async () => {
    try {
      await editProfileMutation.mutateAsync({
        firstName: formData.firstName,
        lastName: formData.lastName,
        interestedTopics: formData.interestedTopics,
        phoneNumber: formData.phoneNumber,
        gender: formData.gender,
      });
      setIsEditing(false);
    } catch (error) {
      // Update failed
    }
  };

  const handleSignOut = async () => {
    await Session.signOut();

    // IMPORTANT: cleanup all the context state here
    cleanup();
    queryClient.clear();

    navigate("/login", { replace: true });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="flex items-center justify-center h-screen">
        <p className="text-gray-500">Failed to load profile</p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm">
        {/* Header Section */}
        <div className="relative">
          <div className="h-32 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-t-lg"></div>
          <div className="absolute -bottom-16 left-8">
            <div className="relative">
              <div className="w-32 h-32 rounded-full border-4 border-white bg-gray-200 overflow-hidden">
                {profile.avatarUrl ? (
                  <img
                    src={profile.avatarUrl}
                    alt={`${profile.firstName} ${profile.lastName}`}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-gray-400 to-gray-600 flex items-center justify-center">
                    <span className="text-white text-4xl font-semibold">
                      {profile.firstName?.charAt(0).toUpperCase() ||
                        profile.email?.charAt(0).toUpperCase() ||
                        "U"}
                    </span>
                  </div>
                )}
              </div>
              <button className="absolute bottom-0 right-0 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow">
                <Camera className="w-5 h-5 text-gray-600" />
              </button>
            </div>
          </div>
        </div>

        {/* Profile Content */}
        <div className="pt-20 px-8 pb-8">
          <div className="flex justify-between items-start mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {isEditing ? (
                  <div className="flex gap-4">
                    <input
                      type="text"
                      value={formData.firstName}
                      onChange={(e) =>
                        setFormData({ ...formData, firstName: e.target.value })
                      }
                      className="px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      placeholder="First Name"
                    />
                    <input
                      type="text"
                      value={formData.lastName}
                      onChange={(e) =>
                        setFormData({ ...formData, lastName: e.target.value })
                      }
                      className="px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      placeholder="Last Name"
                    />
                  </div>
                ) : (
                  `${profile.firstName || ""} ${
                    profile.lastName || ""
                  }`.trim() || "Unnamed User"
                )}
              </h1>
              <p className="text-gray-600 mt-1">{profile.email}</p>
            </div>

            {!isEditing ? (
              <button
                onClick={handleEdit}
                className="flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              >
                <Edit2 className="w-4 h-4" />
                Edit Profile
              </button>
            ) : (
              <div className="flex gap-2">
                <button
                  onClick={handleSave}
                  disabled={editProfileMutation.isPending}
                  className="flex items-center gap-2 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition-colors disabled:opacity-50"
                >
                  <Save className="w-4 h-4" />
                  Save
                </button>
                <button
                  onClick={handleCancel}
                  className="flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                >
                  <X className="w-4 h-4" />
                  Cancel
                </button>
              </div>
            )}
          </div>

          {/* Profile Details */}
          <div className="space-y-6">
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">
                Phone Number
              </h3>
              {isEditing ? (
                <input
                  type="tel"
                  value={formData.phoneNumber}
                  onChange={(e) =>
                    setFormData({ ...formData, phoneNumber: e.target.value })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  placeholder="Enter phone number"
                />
              ) : (
                <p className="text-gray-900">
                  {profile.phoneNumber || "Not provided"}
                </p>
              )}
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">Role</h3>
              <p className="text-gray-900">{profile.role || "User"}</p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">Gender</h3>
              {isEditing ? (
                <select
                  value={formData.gender || ""}
                  onChange={(e) =>
                    setFormData({ ...formData, gender: e.target.value || null })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  <option value="">Not specified</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                </select>
              ) : (
                <p className="text-gray-900">
                  {profile.gender || "Not specified"}
                </p>
              )}
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">
                Interested Topics
              </h3>
              {isEditing ? (
                <textarea
                  value={formData.interestedTopics.join(", ")}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      interestedTopics: e.target.value
                        .split(",")
                        .map((t) => t.trim())
                        .filter(Boolean),
                    })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  rows={3}
                  placeholder="Enter topics separated by commas"
                />
              ) : (
                <div className="flex flex-wrap gap-2">
                  {profile.interestedTopics &&
                  profile.interestedTopics.length > 0 ? (
                    profile.interestedTopics.map((topic, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-indigo-100 text-indigo-700 rounded-full text-sm"
                      >
                        {topic}
                      </span>
                    ))
                  ) : (
                    <p className="text-gray-500">No topics selected</p>
                  )}
                </div>
              )}
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">
                Member Since
              </h3>
              <p className="text-gray-900">
                {new Date(profile.createdAt).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </p>
            </div>
          </div>

          {/* Logout Button */}
          <div className="mt-8 pt-8 border-t border-gray-200">
            <button
              onClick={handleSignOut}
              className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
            >
              <LogOut size={20} />
              <span>Logout</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
