import {
  type RouteConfig,
  index,
  route,
  layout,
  prefix,
} from "@react-router/dev/routes";

export default [
  route("login", "routes/login.tsx"),
  route("signup", "routes/signup.tsx"),
  route("auth/callback/google", "routes/auth/callback/google.tsx"),

  layout("components/AppLayout.tsx", [
    route("setup-profile", "routes/setup-profile.tsx"),
    index("routes/home.tsx"),
    route("explore", "routes/explore.tsx"),
    route(
      "explore/groups/:groupId",
      "routes/explore/groups/[groupId]/index.tsx"
    ),
    route("chat", "routes/chat.tsx"),
    route("notifications", "routes/notifications.tsx"),
    route("profile", "routes/profile.tsx"),

    layout("components/GroupLayout.tsx", [
      ...prefix("/groups/:groupId", [
        route("", "routes/groups/[groupId]/index.tsx"),
        route(
          "cohorts/:cohortId",
          "routes/groups/[groupId]/cohorts/[cohortId]/index.tsx"
        ),
        route(
          "cohorts/:cohortId/info",
          "routes/groups/[groupId]/cohorts/[cohortId]/info.tsx"
        ),
        route(
          "cohorts/:cohortId/modules/:moduleId",
          "routes/groups/[groupId]/cohorts/[cohortId]/modules/[moduleId].tsx"
        ),
        route(
          "cohorts/:cohortId/courses/:courseId/lessons/:lessonId",
          "routes/groups/[groupId]/cohorts/[cohortId]/courses/[courseId]/lessons/[lessonId].tsx"
        ),
      ]),
    ]),
  ]),
] satisfies RouteConfig;
